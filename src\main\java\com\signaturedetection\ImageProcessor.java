package com.signaturedetection;

import org.opencv.core.*;
import org.opencv.imgcodecs.Imgcodecs;
import org.opencv.imgproc.Imgproc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import nu.pattern.OpenCV;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.awt.image.DataBufferByte;
import java.io.File;
import java.io.IOException;

/**
 * Utility class for image processing operations including loading TIFF images,
 * preprocessing with noise removal, and saving processed images.
 */
public class ImageProcessor {
    private static final Logger logger = LoggerFactory.getLogger(ImageProcessor.class);

    // Load OpenCV native library
    static {
        try {
            OpenCV.loadLocally();
            logger.info("OpenCV library loaded successfully in ImageProcessor");
        } catch (Exception e) {
            logger.error("Failed to load OpenCV library in ImageProcessor", e);
            throw new RuntimeException("Failed to load OpenCV library", e);
        }
    }

    /**
     * Loads a TIFF image from the specified path and converts it to OpenCV Mat format.
     * 
     * @param inputPath Path to the input TIFF image
     * @return OpenCV Mat containing the loaded image
     * @throws IOException if the image cannot be loaded
     */
    public static Mat loadTiffImage(String inputPath) throws IOException {
        logger.info("Loading TIFF image from: {}", inputPath);
        
        try {
            // First try to load with OpenCV
            Mat image = Imgcodecs.imread(inputPath, Imgcodecs.IMREAD_GRAYSCALE);
            
            if (image.empty()) {
                // Fallback to Java ImageIO for complex TIFF formats
                logger.debug("OpenCV failed to load image, trying Java ImageIO fallback");
                BufferedImage bufferedImage = ImageIO.read(new File(inputPath));
                
                if (bufferedImage == null) {
                    throw new IOException("Failed to load image: " + inputPath);
                }
                
                // Convert BufferedImage to Mat
                image = bufferedImageToMat(bufferedImage);
            }
            
            if (image.empty()) {
                throw new IOException("Loaded image is empty: " + inputPath);
            }
            
            logger.info("Successfully loaded image: {}x{} pixels", image.cols(), image.rows());
            return image;
            
        } catch (Exception e) {
            logger.error("Error loading TIFF image: {}", e.getMessage(), e);
            throw new IOException("Failed to load TIFF image: " + inputPath, e);
        }
    }

    /**
     * Preprocesses the image with noise removal and enhancement.
     * 
     * @param originalImage The original image to preprocess
     * @return Preprocessed image with noise removed
     */
    public static Mat preprocessImage(Mat originalImage) {
        logger.debug("Preprocessing image with default noise removal");
        
        if (originalImage.empty()) {
            throw new IllegalArgumentException("Input image is empty");
        }
        
        Mat processed = new Mat();
        originalImage.copyTo(processed);
        
        // Convert to grayscale if needed
        if (processed.channels() > 1) {
            Mat gray = new Mat();
            Imgproc.cvtColor(processed, gray, Imgproc.COLOR_BGR2GRAY);
            processed.release();
            processed = gray;
        }
        
        // Apply Gaussian blur to reduce noise
        Mat blurred = new Mat();
        Imgproc.GaussianBlur(processed, blurred, new Size(3, 3), 0);
        processed.release();
        processed = blurred;
        
        // Apply median filter to remove salt-and-pepper noise
        Mat median = new Mat();
        Imgproc.medianBlur(processed, median, 3);
        processed.release();
        processed = median;
        
        // Apply morphological opening to remove small noise
        Mat kernel = Imgproc.getStructuringElement(Imgproc.MORPH_ELLIPSE, new Size(2, 2));
        Mat opened = new Mat();
        Imgproc.morphologyEx(processed, opened, Imgproc.MORPH_OPEN, kernel);
        processed.release();
        kernel.release();
        processed = opened;
        
        // Enhance contrast using CLAHE (Contrast Limited Adaptive Histogram Equalization)
        CLAHE clahe = Imgproc.createCLAHE(2.0, new Size(8, 8));
        Mat enhanced = new Mat();
        clahe.apply(processed, enhanced);
        processed.release();
        processed = enhanced;
        
        logger.debug("Image preprocessing completed");
        return processed;
    }

    /**
     * Saves an OpenCV Mat as an image file.
     * 
     * @param image The Mat to save
     * @param filePath The output file path
     * @throws IOException if the image cannot be saved
     */
    public static void saveImage(Mat image, String filePath) throws IOException {
        logger.debug("Saving image to: {}", filePath);
        
        if (image.empty()) {
            throw new IllegalArgumentException("Cannot save empty image");
        }
        
        // Ensure output directory exists
        File outputFile = new File(filePath);
        File parentDir = outputFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            if (!parentDir.mkdirs()) {
                throw new IOException("Failed to create output directory: " + parentDir.getAbsolutePath());
            }
        }
        
        // Save the image
        boolean success = Imgcodecs.imwrite(filePath, image);
        
        if (!success) {
            throw new IOException("Failed to save image to: " + filePath);
        }
        
        logger.debug("Successfully saved image: {}", filePath);
    }

    /**
     * Converts a BufferedImage to OpenCV Mat format.
     * 
     * @param bufferedImage The BufferedImage to convert
     * @return OpenCV Mat containing the image data
     */
    private static Mat bufferedImageToMat(BufferedImage bufferedImage) {
        // Convert to grayscale if needed
        BufferedImage grayImage;
        if (bufferedImage.getType() != BufferedImage.TYPE_BYTE_GRAY) {
            grayImage = new BufferedImage(bufferedImage.getWidth(), bufferedImage.getHeight(), BufferedImage.TYPE_BYTE_GRAY);
            grayImage.getGraphics().drawImage(bufferedImage, 0, 0, null);
        } else {
            grayImage = bufferedImage;
        }
        
        // Extract pixel data
        byte[] pixels = ((DataBufferByte) grayImage.getRaster().getDataBuffer()).getData();
        
        // Create Mat
        Mat mat = new Mat(grayImage.getHeight(), grayImage.getWidth(), CvType.CV_8UC1);
        mat.put(0, 0, pixels);
        
        return mat;
    }

    /**
     * Creates a visualization of detected items on the original image.
     * 
     * @param originalImage The original image
     * @param boundingRects List of bounding rectangles to visualize
     * @return Mat with visualization overlay
     */
    public static Mat visualizeDetections(Mat originalImage, java.util.List<Rect> boundingRects) {
        Mat visualization = new Mat();
        
        // Convert to color if grayscale
        if (originalImage.channels() == 1) {
            Imgproc.cvtColor(originalImage, visualization, Imgproc.COLOR_GRAY2BGR);
        } else {
            originalImage.copyTo(visualization);
        }
        
        // Draw bounding rectangles
        Scalar color = new Scalar(0, 255, 0); // Green color
        for (Rect rect : boundingRects) {
            Imgproc.rectangle(visualization, rect.tl(), rect.br(), color, 2);
        }
        
        return visualization;
    }
}
