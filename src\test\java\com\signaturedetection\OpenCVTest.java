package com.signaturedetection;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeAll;
import org.opencv.core.Core;
import org.opencv.core.Mat;
import nu.pattern.OpenCV;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class to verify OpenCV library loading and basic functionality.
 */
public class OpenCVTest {
    
    @BeforeAll
    static void loadOpenCV() {
        try {
            OpenCV.loadLocally();
            System.out.println("OpenCV library loaded successfully for testing");
        } catch (Exception e) {
            fail("Failed to load OpenCV library: " + e.getMessage());
        }
    }
    
    @Test
    void testOpenCVLoaded() {
        // Test that OpenCV is properly loaded by checking the version
        String version = Core.VERSION;
        assertNotNull(version, "OpenCV version should not be null");
        assertFalse(version.isEmpty(), "OpenCV version should not be empty");
        System.out.println("OpenCV version: " + version);
    }
    
    @Test
    void testMatCreation() {
        // Test basic Mat creation to ensure OpenCV classes work
        Mat mat = new Mat(10, 10, 0);
        assertNotNull(mat, "Mat should be created successfully");
        assertEquals(10, mat.rows(), "Mat should have 10 rows");
        assertEquals(10, mat.cols(), "Mat should have 10 columns");
        
        // Clean up
        mat.release();
    }
    
    @Test
    void testSignatureDetectorInstantiation() {
        // Test that our SignatureAndInitialsDetector class can be instantiated
        // and doesn't throw any OpenCV-related errors
        assertDoesNotThrow(() -> {
            // This should not throw any exceptions if OpenCV is properly loaded
            SignatureAndInitialsDetector.DetectionConfig config = SignatureAndInitialsDetector.DetectionConfig.createDefault();
            assertNotNull(config, "DetectionConfig should be created successfully");
        });
    }
}
